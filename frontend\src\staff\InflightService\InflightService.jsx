import { useNavigate, useLocation } from 'react-router-dom'
import { passengerService, flightService } from '../../api'
// Passenger details will live on a separate page; navigation will be used to open it
import { useState, useEffect } from 'react'

export default function InflightService() {
  const navigate = useNavigate()
  const { state } = useLocation()
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredPassengers, setFilteredPassengers] = useState([])
  const [selectedPassengerId, setSelectedPassengerId] = useState(null)
  const [selectedSubService, setSelectedSubService] = useState({})
  const [passengers, setPassengers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const flightId = state?.flightId || null

  // Load passengers for the flight
  useEffect(() => {
    if (flightId) {
      loadPassengers()
    }
  }, [flightId])

  const loadPassengers = async () => {
    try {
      setLoading(true)
      const result = await passengerService.getPassengersByFlight(flightId)
      if (result.success) {
        const normalizedPassengers = result.data.map((p) => ({
          ...p,
          // unify possible shopping arrays
          shoppingRequests: p.shoppingRequests || p.shoppingItems || [],
          // unify ancillary services: prefer explicit ancillaryServices, otherwise fall back to services
          ancillaryServices: p.ancillaryServices || p.ancillary || p.services || [],
          // unify special meal display
          specialMeal: p.specialMeal || p.mealName || '',
          // normalize explicit meal type where available (e.g. 'Veg' / 'Non-Veg')
          mealType: p.mealType || p.mealPreference || '',
          // ensure seat exists
          seat: p.seat || '',
        }))

        setPassengers(normalizedPassengers)
        setFilteredPassengers(normalizedPassengers)
      } else {
        setError(result.error || 'Failed to load passengers')
      }
    } catch (err) {
      setError('An error occurred while loading passengers')
      console.error('Error loading passengers:', err)
    } finally {
      setLoading(false)
    }
  }

  // normalize passenger fields for this view
  useEffect(() => {
    if (passengers.length > 0) {
      setFilteredPassengers(passengers)
    }
  }, [passengers])

  // find the flight object for seat map rendering (placeholder for now)
  const flight = { id: flightId, totalSeats: 180, seatLayout: '3-3' }

  // build a quick lookup of passengers by numeric seat row (e.g. 12 from '12A')
  const seatPassengersMap = new Map()
  filteredPassengers.forEach((p) => {
    const row = parseInt((p.seat || '').toString().match(/^\d+/)?.[0], 10)
    if (!isNaN(row)) {
      if (!seatPassengersMap.has(row)) seatPassengersMap.set(row, [])
      seatPassengersMap.get(row).push(p)
    }
  })

  const handleBack = () => {
    // go back to staff landing
    navigate('/staff')
  }

  const handleAddAncillary = (passengerId) => {
    const item = window.prompt('Enter ancillary service to add (e.g. Extra Pillow)')
    if (!item) return
    setFilteredPassengers((prev) =>
      prev.map((p) =>
        p.id === passengerId ? { ...p, ancillaryServices: [...(p.ancillaryServices || []), item] } : p
      )
    )
  }

  const handleChangeMeal = (passengerId) => {
    const meal = window.prompt('Enter new meal name (e.g. Vegetarian Biryani)')
    if (meal === null) return
    setFilteredPassengers((prev) =>
      prev.map((p) => (p.id === passengerId ? { ...p, specialMeal: meal } : p))
    )
  }

  const handleAddShopping = (passengerId) => {
    const item = window.prompt('Enter shopping item to add (e.g. Headphones)')
    if (!item) return
    setFilteredPassengers((prev) =>
      prev.map((p) =>
        p.id === passengerId ? { ...p, shoppingRequests: [...(p.shoppingRequests || []), item] } : p
      )
    )
  }

  const searchResults = filteredPassengers.filter((passenger) =>
    passenger.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    passenger.seat.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-50">
        <div className="text-lg">Loading passengers...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-slate-50 p-6">
      <div className="flex items-center gap-4 mb-6">
        <button onClick={handleBack} className="text-indigo-600 hover:text-indigo-800">
          ←
        </button>
        <h1 className="text-2xl font-semibold">Inflight Service Page</h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 w-full max-w-4xl">
          <strong>Error:</strong> {error}
        </div>
      )}

      <div className="p-6 w-full">
        {flightId && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-2">Flight {flightId} - Passenger Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              <div className="bg-green-100 p-4 rounded">
                <h3 className="font-semibold text-green-800">Vegetarian Meals</h3>
                <p className="text-2xl font-bold text-green-600">
                  {filteredPassengers.filter(p => p.mealType?.toLowerCase().includes('veg')).length}
                </p>
              </div>
              <div className="bg-red-100 p-4 rounded">
                <h3 className="font-semibold text-red-800">Non-Vegetarian Meals</h3>
                <p className="text-2xl font-bold text-red-600">
                  {filteredPassengers.filter(p => p.mealType?.toLowerCase().includes('non')).length}
                </p>
              </div>
              <div className="bg-blue-100 p-4 rounded">
                <h3 className="font-semibold text-blue-800">Total Passengers</h3>
                <p className="text-2xl font-bold text-blue-600">{filteredPassengers.length}</p>
              </div>
            </div>

                return (
                  <div
                    key={s.number}
                    title={title}
                    className={`border border-gray-300 px-2 py-3 rounded text-center text-sm ${colorClass}`}
                  >
                    <div className="font-semibold">{s.number}</div>
                    <div className="text-xs">{smallText}</div>
                  </div>
                )
              })}
            </div>
          </div>
        )}
        <div className="mb-4">
          <input
            type="text"
            placeholder="Search by passenger name or seat number"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="px-4 py-2 border rounded w-full"
          />
        </div>

        <h2 className="text-xl font-semibold mb-4">Passenger List</h2>
        <table className="table-auto w-full border-collapse border border-gray-300">
          <thead>
            <tr>
              <th className="border border-gray-300 px-4 py-2">Passenger Name</th>
              <th className="border border-gray-300 px-4 py-2">Seat</th>
              <th className="border border-gray-300 px-4 py-2">Special Meal</th>
              <th className="border border-gray-300 px-4 py-2">Ancillary Services</th>
              <th className="border border-gray-300 px-4 py-2">Shopping Requests</th>
              <th className="border border-gray-300 px-4 py-2">Services</th>
            </tr>
          </thead>
          <tbody>
            {searchResults.map((passenger) => (
              <tr key={passenger.id} className={passenger.specialMeal ? 'bg-yellow-100' : ''}>
                <td className="border border-gray-300 px-4 py-2">
                  <span
                    className="text-indigo-600 cursor-pointer hover:underline"
                    onClick={() => navigate(`/inflight-service/passenger/${passenger.id}`, { state: { passenger, flightId: passenger.flightId } })}
                  >
                    {passenger.name}
                  </span>
                </td>
                <td className="border border-gray-300 px-4 py-2">{passenger.seat}</td>
                <td className="border border-gray-300 px-4 py-2">{passenger.specialMeal || 'None'}</td>
                <td className="border border-gray-300 px-4 py-2">
                  {passenger.ancillaryServices && passenger.ancillaryServices.length > 0
                    ? passenger.ancillaryServices.join(', ')
                    : 'None'}
                </td>
                <td className="border border-gray-300 px-4 py-2">
                  {passenger.shoppingRequests && passenger.shoppingRequests.length > 0
                    ? passenger.shoppingRequests.join(', ')
                    : 'None'}
                </td>
                <td className="border border-gray-300 px-4 py-2">
                  <div className="flex flex-col gap-2">
                    <div className="text-sm">{passenger.ancillaryServices && passenger.ancillaryServices.length > 0 ? passenger.ancillaryServices.join(', ') : 'No ancillaries'}</div>
                    <div className="text-sm">{passenger.specialMeal || 'No meal'}</div>
                    <div className="text-sm">{passenger.shoppingRequests && passenger.shoppingRequests.length > 0 ? passenger.shoppingRequests.join(', ') : 'No shopping'}</div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
  {/* Passenger details are now shown on their own page at /inflight-service/passenger/:id */}
      </div>
    </div>
  )
}
