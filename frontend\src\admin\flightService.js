import { flightService } from '../api'

// In-memory cache for flights with listeners for reactive updates
let flightsCache = []
const listeners = new Set()

// Emit changes to all listeners
function emit() {
  listeners.forEach(cb => cb(flightsCache.slice()))
}

// Subscribe to flight changes
export function subscribe(cb) {
  listeners.add(cb)
  return () => listeners.delete(cb)
}

// Get all flights
export async function list() {
  try {
    const result = await flightService.getAllFlights()
    if (result.success) {
      flightsCache = result.data
      emit()
      return flightsCache.slice()
    } else {
      console.error('Failed to fetch flights:', result.error)
      return flightsCache.slice() // Return cached data on error
    }
  } catch (error) {
    console.error('Error fetching flights:', error)
    return flightsCache.slice()
  }
}

// Find flight by ID
export async function findById(id) {
  try {
    const result = await flightService.getFlightById(id)
    if (result.success) {
      return result.data
    } else {
      console.error('Failed to fetch flight:', result.error)
      // Fallback to cache
      return flightsCache.find(f => f.id === Number(id))
    }
  } catch (error) {
    console.error('Error fetching flight:', error)
    return flightsCache.find(f => f.id === Number(id))
  }
}

// Add or update flight
export async function addOrUpdate(flightData) {
  try {
    let result

    if (flightData.id) {
      // Update existing flight
      result = await flightService.updateFlight(flightData.id, flightData)
    } else {
      // Create new flight
      result = await flightService.createFlight(flightData)
    }

    if (result.success) {
      // Refresh the flights list
      await list()
      return { success: true, data: result.data }
    } else {
      console.error('Failed to save flight:', result.error)
      return { success: false, error: result.error }
    }
  } catch (error) {
    console.error('Error saving flight:', error)
    return { success: false, error: error.message }
  }
}

// Remove flight
export async function remove(id) {
  try {
    const result = await flightService.deleteFlight(id)
    if (result.success) {
      // Refresh the flights list
      await list()
      return { success: true }
    } else {
      console.error('Failed to delete flight:', result.error)
      return { success: false, error: result.error }
    }
  } catch (error) {
    console.error('Error deleting flight:', error)
    return { success: false, error: error.message }
  }
}

// Initialize flights cache on module load
list().catch(console.error)
