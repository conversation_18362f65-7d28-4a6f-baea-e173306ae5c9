import { useState, useEffect } from 'react'
import { travelHistoryService, passengerService } from '../api'

export default function TravelHistory() {
  const [history, setHistory] = useState([])
  const [passengers, setPassengers] = useState([])
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState('All')
  const [selectedPassenger, setSelectedPassenger] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    if (selectedPassenger) {
      loadPassengerHistory(selectedPassenger)
    } else {
      setHistory([])
    }
  }, [selectedPassenger])

  const loadData = async () => {
    try {
      setLoading(true)
      // Load passengers for dropdown
      const passengersResult = await passengerService.getAllPassengers()
      if (passengersResult.success) {
        setPassengers(passengersResult.data)
      } else {
        setError(passengersResult.error || 'Failed to load passengers')
      }
    } catch (err) {
      setError('An error occurred while loading data')
      console.error('Error loading data:', err)
    } finally {
      setLoading(false)
    }
  }

  const loadPassengerHistory = async (passengerId) => {
    try {
      const result = await travelHistoryService.getPassengerTravelHistory(passengerId)
      if (result.success) {
        setHistory(result.data)
        setError('')
      } else {
        setError(result.error || 'Failed to load travel history')
        setHistory([])
      }
    } catch (err) {
      setError('An error occurred while loading travel history')
      setHistory([])
      console.error('Error loading travel history:', err)
    }
  }

  const searchBookingReference = async () => {
    if (!search.trim()) return

    try {
      const result = await travelHistoryService.getBookingByReference(search.trim())
      if (result.success) {
        setHistory([result.data])
        setError('')
      } else {
        setError(result.error || 'Booking not found')
        setHistory([])
      }
    } catch (err) {
      setError('An error occurred while searching')
      setHistory([])
      console.error('Error searching booking:', err)
    }
  }

  const filtered = history.filter(h => {
    const matchesSearch = !search ||
      h.bookingReference?.toLowerCase().includes(search.toLowerCase()) ||
      h.origin?.toLowerCase().includes(search.toLowerCase()) ||
      h.destination?.toLowerCase().includes(search.toLowerCase())

    const matchesStatus = statusFilter === 'All' || h.status === statusFilter

    return matchesSearch && matchesStatus
  })

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Loading travel history...</div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-6">Travel History</h2>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
          <button
            onClick={() => setError('')}
            className="ml-2 text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Passenger
          </label>
          <select
            value={selectedPassenger}
            onChange={(e) => setSelectedPassenger(e.target.value)}
            className="border rounded px-3 py-2 w-full"
          >
            <option value="">Select a passenger...</option>
            {passengers.map(passenger => (
              <option key={passenger.id} value={passenger.id}>
                {passenger.name} (ID: {passenger.id})
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Search by Booking Reference
          </label>
          <div className="flex">
            <input
              type="text"
              placeholder="Enter booking reference..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="border rounded-l px-3 py-2 flex-1"
              onKeyPress={(e) => e.key === 'Enter' && searchBookingReference()}
            />
            <button
              onClick={searchBookingReference}
              className="bg-blue-500 text-white px-4 py-2 rounded-r hover:bg-blue-600"
            >
              Search
            </button>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Filter by Status
          </label>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="border rounded px-3 py-2 w-full"
          >
            <option value="All">All Status</option>
            <option value="Completed">Completed</option>
            <option value="Checked-in">Checked-in</option>
            <option value="Booked">Booked</option>
            <option value="Cancelled">Cancelled</option>
            <option value="Pending">Pending</option>
          </select>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Booking Ref
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Route
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Passenger
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Seat
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Class
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filtered.length === 0 ? (
              <tr>
                <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                  {selectedPassenger || search ? 'No travel history found' : 'Select a passenger or search by booking reference'}
                </td>
              </tr>
            ) : (
              filtered.map((record) => (
                <tr key={record.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {record.bookingReference}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {record.origin} → {record.destination}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {record.date}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    Passenger ID: {record.passengerId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {record.seat || 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      record.status === 'Completed' ? 'bg-green-100 text-green-800' :
                      record.status === 'Checked-in' ? 'bg-blue-100 text-blue-800' :
                      record.status === 'Booked' ? 'bg-yellow-100 text-yellow-800' :
                      record.status === 'Cancelled' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {record.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {record.fareClass}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
