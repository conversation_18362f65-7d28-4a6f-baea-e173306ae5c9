-- =====================================================
-- AIRLINE MANAGEMENT SYSTEM - ORACLE DATABASE SCHEMA
-- =====================================================

-- Drop existing tables if they exist (for clean setup)
BEGIN
   FOR c IN (SELECT table_name FROM user_tables WHERE table_name IN (
      'TRAVEL_HISTORY', 'PASSENGERS', 'FLIGHTS', 'USERS'
   )) LOOP
      EXECUTE IMMEDIATE 'DROP TABLE ' || c.table_name || ' CASCADE CONSTRAINTS';
   END LOOP;
END;
/

-- =====================================================
-- USERS TABLE - Authentication and Role Management
-- =====================================================
CREATE TABLE users (
    user_id         NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    username        VARCHAR2(50) UNIQUE NOT NULL,
    password        VARCHAR2(255) NOT NULL,
    role            VARCHAR2(30) NOT NULL CHECK (role IN ('admin', 'inflightStaff', 'checkinStaff', 'passenger')),
    name            VARCHAR2(100) NOT NULL,
    email           VARCHAR2(100),
    phone_number    VARCHAR2(20),
    flight_id       NUMBER,  -- For staff members assigned to specific flights
    created_at      TIMESTAMP DEFAULT SYSTIMESTAMP,
    last_login      TIMESTAMP
);

-- Create index for faster login queries
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);

-- =====================================================
-- FLIGHTS TABLE
-- =====================================================
CREATE TABLE flights (
    flight_id           NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    flight_name         VARCHAR2(50) NOT NULL,
    flight_date         DATE NOT NULL,
    route               VARCHAR2(20) NOT NULL,  -- e.g., 'NYC-LON'
    departure_time      VARCHAR2(20) NOT NULL,  -- Storing as string to match dummy data
    arrival_time        VARCHAR2(20) NOT NULL,
    aircraft_type       VARCHAR2(50),
    total_seats         NUMBER NOT NULL,
    available_seats     NUMBER NOT NULL,
    services            CLOB CHECK (services IS JSON),  -- JSON array of available services
    service_subtypes    CLOB CHECK (service_subtypes IS JSON),  -- JSON object with service subtypes
    seat_map            CLOB CHECK (seat_map IS JSON),  -- JSON array of seat objects
    created_at          TIMESTAMP DEFAULT SYSTIMESTAMP,
    updated_at          TIMESTAMP DEFAULT SYSTIMESTAMP
);

-- Create indexes for common queries
CREATE INDEX idx_flights_date ON flights(flight_date);
CREATE INDEX idx_flights_route ON flights(route);

-- =====================================================
-- PASSENGERS TABLE
-- =====================================================
CREATE TABLE passengers (
    passenger_id        NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    flight_id           NUMBER NOT NULL,
    name                VARCHAR2(100) NOT NULL,
    phone_number        VARCHAR2(20),
    address             VARCHAR2(300),
    passport_number     VARCHAR2(30),
    date_of_birth       DATE,
    origin              VARCHAR2(10) NOT NULL,  -- 'from' in dummy data
    destination         VARCHAR2(10) NOT NULL,  -- 'to' in dummy data
    services            CLOB CHECK (services IS JSON),  -- JSON array of services
    meal_type           VARCHAR2(50),
    meal_name           VARCHAR2(100),
    extra_baggage       NUMBER DEFAULT 0,
    shopping_items      CLOB CHECK (shopping_items IS JSON),  -- JSON array
    seat                VARCHAR2(10),
    checked_in          CHAR(1) DEFAULT 'N' CHECK (checked_in IN ('Y', 'N')),
    wheelchair          CHAR(1) DEFAULT 'N' CHECK (wheelchair IN ('Y', 'N')),
    infant              CHAR(1) DEFAULT 'N' CHECK (infant IN ('Y', 'N')),
    created_at          TIMESTAMP DEFAULT SYSTIMESTAMP,
    updated_at          TIMESTAMP DEFAULT SYSTIMESTAMP,
    
    CONSTRAINT fk_passengers_flight
        FOREIGN KEY (flight_id) REFERENCES flights(flight_id) ON DELETE CASCADE
);

-- Create indexes for common queries
CREATE INDEX idx_passengers_flight ON passengers(flight_id);
CREATE INDEX idx_passengers_name ON passengers(name);
CREATE INDEX idx_passengers_checkin ON passengers(checked_in);

-- =====================================================
-- TRAVEL HISTORY TABLE
-- =====================================================
CREATE TABLE travel_history (
    history_id          NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    passenger_id        NUMBER NOT NULL,
    flight_id           NUMBER NOT NULL,
    travel_date         DATE NOT NULL,
    origin              VARCHAR2(10) NOT NULL,
    destination         VARCHAR2(10) NOT NULL,
    seat                VARCHAR2(10),
    booking_reference   VARCHAR2(20) UNIQUE NOT NULL,
    fare_class          VARCHAR2(30),
    status              VARCHAR2(20) CHECK (status IN ('Completed', 'Checked-in', 'Cancelled', 'Pending', 'Booked')),
    distance_km         NUMBER,
    duration_min        NUMBER,
    notes               VARCHAR2(500),
    created_at          TIMESTAMP DEFAULT SYSTIMESTAMP,
    
    CONSTRAINT fk_history_passenger
        FOREIGN KEY (passenger_id) REFERENCES passengers(passenger_id) ON DELETE CASCADE,
    CONSTRAINT fk_history_flight
        FOREIGN KEY (flight_id) REFERENCES flights(flight_id) ON DELETE CASCADE
);

-- Create indexes for travel history queries
CREATE INDEX idx_history_passenger ON travel_history(passenger_id);
CREATE INDEX idx_history_flight ON travel_history(flight_id);
CREATE INDEX idx_history_date ON travel_history(travel_date);
CREATE INDEX idx_history_booking_ref ON travel_history(booking_reference);

-- =====================================================
-- INSERT DUMMY DATA
-- =====================================================

-- Insert Users
INSERT INTO users (username, password, role, name, email, phone_number, flight_id) VALUES
    ('admin1', 'adminpass', 'admin', 'Alice', NULL, NULL, NULL);
INSERT INTO users (username, password, role, name, flight_id) VALUES
    ('inflight1', 'inflightpass', 'inflightStaff', 'Charlie Inflight', 1);
INSERT INTO users (username, password, role, name, flight_id) VALUES
    ('checkin1', 'checkinpass', 'checkinStaff', 'Diana Checkin', 2);
INSERT INTO users (username, password, role, name, email, phone_number) VALUES
    ('passenger1', 'passpass', 'passenger', 'Sam Traveler', '<EMAIL>', '************');

-- Insert Flights
INSERT INTO flights (
    flight_name, flight_date, route, departure_time, arrival_time, 
    aircraft_type, total_seats, available_seats, services, service_subtypes, seat_map
) VALUES (
    'Flight 101', 
    DATE '2025-08-20', 
    'NYC-LON', 
    '08:00 AM', 
    '04:00 PM',
    'Boeing 747', 
    20, 
    17,
    '["Ancillary", "Meal", "Shopping"]',
    '{
        "Ancillary": ["Extra Baggage 5kg", "Extra Baggage 10kg", "Priority Boarding"],
        "Meal": ["Veg", "Non-Veg", "Vegan", "Gluten-Free"],
        "Shopping": ["Magazine", "Perfume", "Sunglasses", "Headphones"]
    }',
    '[{"number": 1, "isBooked": false}, {"number": 2, "isBooked": false}, {"number": 3, "isBooked": false}, 
      {"number": 4, "isBooked": false}, {"number": 5, "isBooked": false}, {"number": 6, "isBooked": false},
      {"number": 7, "isBooked": false}, {"number": 8, "isBooked": false}, {"number": 9, "isBooked": false},
      {"number": 10, "isBooked": false}, {"number": 11, "isBooked": false}, {"number": 12, "isBooked": true},
      {"number": 13, "isBooked": false}, {"number": 14, "isBooked": true}, {"number": 15, "isBooked": true},
      {"number": 16, "isBooked": false}, {"number": 17, "isBooked": false}, {"number": 18, "isBooked": false},
      {"number": 19, "isBooked": false}, {"number": 20, "isBooked": false}]'
);

INSERT INTO flights (
    flight_name, flight_date, route, departure_time, arrival_time, 
    aircraft_type, total_seats, available_seats, services, service_subtypes, seat_map
) VALUES (
    'Flight 202', 
    DATE '2025-08-21', 
    'PAR-TOK', 
    '09:00 AM', 
    '11:00 PM',
    'Airbus A380', 
    30, 
    28,
    '["Ancillary", "Meal"]',
    '{
        "Ancillary": ["Extra Baggage 5kg", "Priority Boarding"],
        "Meal": ["Veg", "Non-Veg", "Kosher"]
    }',
    '[{"number": 1, "isBooked": false}, {"number": 2, "isBooked": false}, {"number": 3, "isBooked": false},
      {"number": 4, "isBooked": false}, {"number": 5, "isBooked": false}, {"number": 6, "isBooked": false},
      {"number": 7, "isBooked": false}, {"number": 8, "isBooked": false}, {"number": 9, "isBooked": false},
      {"number": 10, "isBooked": true}, {"number": 11, "isBooked": true}, {"number": 12, "isBooked": false},
      {"number": 13, "isBooked": false}, {"number": 14, "isBooked": false}, {"number": 15, "isBooked": false},
      {"number": 16, "isBooked": false}, {"number": 17, "isBooked": false}, {"number": 18, "isBooked": false},
      {"number": 19, "isBooked": false}, {"number": 20, "isBooked": false}, {"number": 21, "isBooked": false},
      {"number": 22, "isBooked": false}, {"number": 23, "isBooked": false}, {"number": 24, "isBooked": false},
      {"number": 25, "isBooked": false}, {"number": 26, "isBooked": false}, {"number": 27, "isBooked": false},
      {"number": 28, "isBooked": false}, {"number": 29, "isBooked": false}, {"number": 30, "isBooked": false}]'
);

INSERT INTO flights (
    flight_name, flight_date, route, departure_time, arrival_time, 
    aircraft_type, total_seats, available_seats, services, service_subtypes, seat_map
) VALUES (
    'Flight 303', 
    DATE '2025-08-22', 
    'LAX-SYD', 
    '10:00 AM', 
    '06:00 AM',
    'Boeing 777', 
    30, 
    29,
    '["Shopping"]',
    '{
        "Shopping": ["Souvenir", "Duty-Free Liquor", "Travel Adapter"]
    }',
    '[{"number": 1, "isBooked": false}, {"number": 2, "isBooked": false}, {"number": 3, "isBooked": false},
      {"number": 4, "isBooked": false}, {"number": 5, "isBooked": true}, {"number": 6, "isBooked": false},
      {"number": 7, "isBooked": false}, {"number": 8, "isBooked": false}, {"number": 9, "isBooked": false},
      {"number": 10, "isBooked": false}, {"number": 11, "isBooked": false}, {"number": 12, "isBooked": false},
      {"number": 13, "isBooked": false}, {"number": 14, "isBooked": false}, {"number": 15, "isBooked": false},
      {"number": 16, "isBooked": false}, {"number": 17, "isBooked": false}, {"number": 18, "isBooked": false},
      {"number": 19, "isBooked": false}, {"number": 20, "isBooked": false}, {"number": 21, "isBooked": false},
      {"number": 22, "isBooked": false}, {"number": 23, "isBooked": false}, {"number": 24, "isBooked": false},
      {"number": 25, "isBooked": false}, {"number": 26, "isBooked": false}, {"number": 27, "isBooked": false},
      {"number": 28, "isBooked": false}, {"number": 29, "isBooked": false}, {"number": 30, "isBooked": false}]'
);

-- Insert Passengers
INSERT INTO passengers (
    flight_id, name, phone_number, address, passport_number, date_of_birth,
    origin, destination, services, meal_type, meal_name, extra_baggage, 
    shopping_items, seat, checked_in, wheelchair, infant
) VALUES (
    1, 'Alice Johnson', '************', '123 Main St, New York, NY', 'A1234567', DATE '1990-04-15',
    'NYC', 'LON', '["Meal", "Ancillary"]', 'Veg', 'Biryani', 10,
    '[]', '12A', 'Y', 'N', 'N'
);

INSERT INTO passengers (
    flight_id, name, phone_number, address, passport_number, date_of_birth,
    origin, destination, services, meal_type, meal_name, extra_baggage, 
    shopping_items, seat, checked_in, wheelchair, infant
) VALUES (
    1, 'Bob Smith', '************', '456 Elm St, Los Angeles, CA', NULL, NULL,
    'NYC', 'LON', '["Shopping"]', NULL, NULL, 0,
    '["Magazine", "Perfume"]', '14B', 'Y', 'Y', 'N'
);

INSERT INTO passengers (
    flight_id, name, phone_number, address, passport_number, date_of_birth,
    origin, destination, services, meal_type, meal_name, extra_baggage, 
    shopping_items, seat, checked_in, wheelchair, infant
) VALUES (
    1, 'Charlie Brown', '************', '789 Oak St, Chicago, IL', 'B7654321', DATE '1985-11-05',
    'NYC', 'LON', '["Meal", "Shopping"]', 'Non-Veg', 'Burger', 0,
    '["Chocolates"]', '15C', 'Y', 'N', 'Y'
);

INSERT INTO passengers (
    flight_id, name, phone_number, address, passport_number, date_of_birth,
    origin, destination, services, meal_type, meal_name, extra_baggage, 
    shopping_items, seat, checked_in, wheelchair, infant
) VALUES (
    2, 'Diana Prince', '************', '321 Maple St, Paris, FR', 'P9998887', DATE '1992-07-20',
    'PAR', 'TOK', '["Meal"]', 'Veg', 'Salad', 0,
    '[]', '10A', 'Y', 'N', 'N'
);

INSERT INTO passengers (
    flight_id, name, phone_number, address, passport_number, date_of_birth,
    origin, destination, services, meal_type, meal_name, extra_baggage, 
    shopping_items, seat, checked_in, wheelchair, infant
) VALUES (
    2, 'Ethan Hunt', '************', '654 Pine St, Tokyo, JP', 'E5554443', DATE '1978-03-12',
    'PAR', 'TOK', '["Shopping"]', NULL, NULL, 0,
    '["Watch"]', '11B', 'Y', 'N', 'N'
);

INSERT INTO passengers (
    flight_id, name, phone_number, address, passport_number, date_of_birth,
    origin, destination, services, meal_type, meal_name, extra_baggage, 
    shopping_items, seat, checked_in, wheelchair, infant
) VALUES (
    3, 'Fiona Glenanne', '************', '987 Birch St, Sydney, AU', NULL, NULL,
    'LAX', 'SYD', '["Ancillary"]', NULL, NULL, 15,
    '[]', '5C', 'Y', 'Y', 'Y'
);

-- Insert Travel History
INSERT INTO travel_history (
    passenger_id, flight_id, travel_date, origin, destination, seat,
    booking_reference, fare_class, status, distance_km, duration_min, notes
) VALUES (
    1, 1, DATE '2024-12-15', 'NYC', 'LON', '12A',
    'ABC123', 'Economy', 'Completed', 5567, 420, 'On-time arrival'
);

INSERT INTO travel_history (
    passenger_id, flight_id, travel_date, origin, destination, seat,
    booking_reference, fare_class, status, distance_km, duration_min, notes
) VALUES (
    2, 1, DATE '2025-01-10', 'NYC', 'LON', '14B',
    'DEF456', 'Economy', 'Completed', 5567, 430, 'Delayed due to weather'
);

INSERT INTO travel_history (
    passenger_id, flight_id, travel_date, origin, destination, seat,
    booking_reference, fare_class, status, distance_km, duration_min, notes
) VALUES (
    3, 1, DATE '2025-02-05', 'NYC', 'LON', '15C',
    'GHI789', 'Business', 'Completed', 5567, 415, 'Upgraded to Business'
);

INSERT INTO travel_history (
    passenger_id, flight_id, travel_date, origin, destination, seat,
    booking_reference, fare_class, status, distance_km, duration_min, notes
) VALUES (
    4, 2, DATE '2025-03-21', 'PAR', 'TOK', '10A',
    'JKL012', 'Economy', 'Completed', 9712, 840, NULL
);

INSERT INTO travel_history (
    passenger_id, flight_id, travel_date, origin, destination, seat,
    booking_reference, fare_class, status, distance_km, duration_min, notes
) VALUES (
    5, 2, DATE '2025-04-01', 'PAR', 'TOK', '11B',
    'MNO345', 'Premium Economy', 'Checked-in', 9712, 845, 'Checked in online'
);

INSERT INTO travel_history (
    passenger_id, flight_id, travel_date, origin, destination, seat,
    booking_reference, fare_class, status, distance_km, duration_min, notes
) VALUES (
    6, 3, DATE '2025-05-18', 'LAX', 'SYD', '5C',
    'PQR678', 'Economy', 'Cancelled', 12051, 900, 'Cancelled by airline'
);

INSERT INTO travel_history (
    passenger_id, flight_id, travel_date, origin, destination, seat,
    booking_reference, fare_class, status, distance_km, duration_min, notes
) VALUES (
    2, 3, DATE '2025-06-02', 'LAX', 'SYD', NULL,
    'STB999', 'Standby', 'Pending', 12051, 905, 'Standby passenger'
);

INSERT INTO travel_history (
    passenger_id, flight_id, travel_date, origin, destination, seat,
    booking_reference, fare_class, status, distance_km, duration_min, notes
) VALUES (
    1, 2, DATE '2025-12-10', 'PAR', 'TOK', '20C',
    'FUT2025', 'Economy', 'Booked', 9712, 840, 'Return trip'
);

-- Commit all changes
COMMIT;

-- =====================================================
-- CREATE VIEWS FOR EASIER DATA ACCESS
-- =====================================================

-- View: Passenger Details with Flight Information
CREATE OR REPLACE VIEW v_passenger_details AS
SELECT 
    p.passenger_id,
    p.name AS passenger_name,
    p.phone_number,
    p.passport_number,
    p.date_of_birth,
    p.seat,
    CASE WHEN p.checked_in = 'Y' THEN 'Yes' ELSE 'No' END AS checked_in_status,
    CASE WHEN p.wheelchair = 'Y' THEN 'Yes' ELSE 'No' END AS needs_wheelchair,
    CASE WHEN p.infant = 'Y' THEN 'Yes' ELSE 'No' END AS with_infant,
    p.meal_type,
    p.meal_name,
    p.extra_baggage,
    p.services,
    p.shopping_items,
    f.flight_name,
    f.flight_date,
    f.route,
    f.departure_time,
    f.arrival_time,
    f.aircraft_type
FROM 
    passengers p
JOIN 
    flights f ON p.flight_id = f.flight_id;

-- View: Flight Occupancy Status
CREATE OR REPLACE VIEW v_flight_occupancy AS
SELECT 
    flight_id,
    flight_name,
    flight_date,
    route,
    total_seats,
    available_seats,
    (total_seats - available_seats) AS occupied_seats,
    ROUND(((total_seats - available_seats) * 100.0 / total_seats), 2) AS occupancy_percentage
FROM 
    flights;

-- View: Staff Assignment
CREATE OR REPLACE VIEW v_staff_assignments AS
SELECT 
    u.user_id,
    u.username,
    u.name AS staff_name,
    u.role,
    f.flight_name,
    f.flight_date,
    f.route
FROM 
    users u
LEFT JOIN 
    flights f ON u.flight_id = f.flight_id
WHERE 
    u.role IN ('inflightStaff', 'checkinStaff');

-- View: Complete Travel History
CREATE OR REPLACE VIEW v_travel_history_complete AS
SELECT 
    th.history_id,
    p.name AS passenger_name,
    f.flight_name,
    th.travel_date,
    th.origin,
    th.destination,
    th.seat,
    th.booking_reference,
    th.fare_class,
    th.status,
    th.distance_km,
    th.duration_min,
    th.notes
FROM 
    travel_history th
JOIN 
    passengers p ON th.passenger_id = p.passenger_id
JOIN 
    flights f ON th.flight_id = f.flight_id
ORDER BY 
    th.travel_date DESC;

-- =====================================================
-- USEFUL QUERIES FOR TESTING
-- =====================================================

-- Get all passengers for a specific flight
-- SELECT * FROM v_passenger_details WHERE flight_name = 'Flight 101';

-- Get flight occupancy status
-- SELECT * FROM v_flight_occupancy;

-- Get staff assignments
-- SELECT * FROM v_staff_assignments;

-- Get passenger travel history
-- SELECT * FROM v_travel_history_complete WHERE passenger_name = 'Alice Johnson';

-- Get passengers with special needs
-- SELECT passenger_name, flight_name, needs_wheelchair, with_infant 
-- FROM v_passenger_details 
-- WHERE needs_wheelchair = 'Yes' OR with_infant = 'Yes';

-- =====================================================
-- GRANT PERMISSIONS (Adjust based on your needs)
-- =====================================================
-- Example: Create application user and grant permissions
-- CREATE USER airline_app IDENTIFIED BY your_password;
-- GRANT CONNECT, RESOURCE TO airline_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON users TO airline_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON flights TO airline_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON passengers TO airline_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON travel_history TO airline_app;
-- GRANT SELECT ON v_passenger_details TO airline_app;
-- GRANT SELECT ON v_flight_occupancy TO airline_app;
-- GRANT SELECT ON v_staff_assignments TO airline_app;
-- GRANT SELECT ON v_travel_history_complete TO airline_app;