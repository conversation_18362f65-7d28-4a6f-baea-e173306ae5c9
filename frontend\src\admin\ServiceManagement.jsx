import { useState, useEffect } from 'react'
import { serviceManagementService, flightService } from '../api'

export default function ServiceManagement() {
  const [flights, setFlights] = useState([])
  const [selectedFlight, setSelectedFlight] = useState(null)
  const [services, setServices] = useState([])
  const [serviceStats, setServiceStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    loadFlights()
  }, [])

  useEffect(() => {
    if (selectedFlight) {
      loadFlightServices()
      loadServiceStats()
    }
  }, [selectedFlight])

  const loadFlights = async () => {
    try {
      setLoading(true)
      const result = await flightService.getAllFlights()
      if (result.success) {
        setFlights(result.data)
        if (result.data.length > 0) {
          setSelectedFlight(result.data[0])
        }
      } else {
        setError(result.error || 'Failed to load flights')
      }
    } catch (err) {
      setError('An error occurred while loading flights')
      console.error('Error loading flights:', err)
    } finally {
      setLoading(false)
    }
  }

  const loadFlightServices = async () => {
    if (!selectedFlight) return

    try {
      const result = await serviceManagementService.getFlightServices(selectedFlight.id)
      if (result.success) {
        setServices(result.data)
      } else {
        console.error('Failed to load flight services:', result.error)
      }
    } catch (err) {
      console.error('Error loading flight services:', err)
    }
  }

  const loadServiceStats = async () => {
    if (!selectedFlight) return

    try {
      const result = await serviceManagementService.getFlightServiceStats(selectedFlight.id)
      if (result.success) {
        setServiceStats(result.data)
      } else {
        console.error('Failed to load service stats:', result.error)
      }
    } catch (err) {
      console.error('Error loading service stats:', err)
    }
  }

  const loadServicesByType = async (serviceType) => {
    if (!selectedFlight) return

    try {
      let result
      switch (serviceType) {
        case 'meal':
          result = await serviceManagementService.getFlightMealServices(selectedFlight.id)
          break
        case 'baggage':
          result = await serviceManagementService.getFlightBaggageServices(selectedFlight.id)
          break
        case 'shopping':
          result = await serviceManagementService.getFlightShoppingServices(selectedFlight.id)
          break
        default:
          result = await serviceManagementService.getFlightPassengerServices(selectedFlight.id, serviceType)
      }

      if (result.success) {
        setServices(result.data)
      } else {
        setError(result.error || `Failed to load ${serviceType} services`)
      }
    } catch (err) {
      setError(`An error occurred while loading ${serviceType} services`)
      console.error(`Error loading ${serviceType} services:`, err)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Loading service management...</div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Service Management</h2>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
          <button 
            onClick={() => setError('')} 
            className="ml-2 text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      {/* Flight Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Flight
        </label>
        <select
          value={selectedFlight?.id || ''}
          onChange={(e) => {
            const flight = flights.find(f => f.id === parseInt(e.target.value))
            setSelectedFlight(flight)
          }}
          className="border rounded px-3 py-2 w-full max-w-md"
        >
          <option value="">Select a flight...</option>
          {flights.map(flight => (
            <option key={flight.id} value={flight.id}>
              {flight.flightName || flight.name} - {flight.route} ({flight.flightDate || flight.date})
            </option>
          ))}
        </select>
      </div>

      {selectedFlight && (
        <>
          {/* Service Tabs */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', name: 'Overview' },
                { id: 'meal', name: 'Meals' },
                { id: 'baggage', name: 'Baggage' },
                { id: 'shopping', name: 'Shopping' },
                { id: 'ancillary', name: 'Ancillary' }
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id)
                    if (tab.id !== 'overview') {
                      loadServicesByType(tab.id)
                    }
                  }}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          {activeTab === 'overview' && serviceStats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-800">Meal Services</h3>
                <p className="text-2xl font-bold text-blue-600">{serviceStats.mealCount || 0}</p>
                <p className="text-sm text-blue-600">passengers with meals</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-green-800">Baggage Services</h3>
                <p className="text-2xl font-bold text-green-600">{serviceStats.baggageCount || 0}</p>
                <p className="text-sm text-green-600">extra baggage requests</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-purple-800">Shopping Services</h3>
                <p className="text-2xl font-bold text-purple-600">{serviceStats.shoppingCount || 0}</p>
                <p className="text-sm text-purple-600">shopping orders</p>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-orange-800">Ancillary Services</h3>
                <p className="text-2xl font-bold text-orange-600">{serviceStats.ancillaryCount || 0}</p>
                <p className="text-sm text-orange-600">ancillary services</p>
              </div>
            </div>
          )}

          {activeTab !== 'overview' && (
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Passenger
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Seat
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Service Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {services.length === 0 ? (
                    <tr>
                      <td colSpan="4" className="px-6 py-4 text-center text-gray-500">
                        No {activeTab} services found for this flight
                      </td>
                    </tr>
                  ) : (
                    services.map((service, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {service.passengerName || service.name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {service.seat || '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {activeTab === 'meal' && (
                            <div>
                              <div>Type: {service.mealType}</div>
                              {service.mealName && <div>Meal: {service.mealName}</div>}
                            </div>
                          )}
                          {activeTab === 'baggage' && (
                            <div>Extra Baggage: {service.extraBaggage || service.extraBaggageWeight}kg</div>
                          )}
                          {activeTab === 'shopping' && (
                            <div>Items: {service.shoppingItems?.join(', ') || 'N/A'}</div>
                          )}
                          {activeTab === 'ancillary' && (
                            <div>{service.serviceType || 'Ancillary service'}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            Active
                          </span>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}
    </div>
  )
}
