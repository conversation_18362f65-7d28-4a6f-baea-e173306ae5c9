// User Management Service
// Handles all user management API operations

import apiClient from './apiClient.js';

class UserService {
  // Get all users
  async getAllUsers() {
    try {
      const response = await apiClient.get('/users');
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch users'
      };
    }
  }

  // Get user by ID
  async getUserById(id) {
    try {
      const response = await apiClient.get(`/users/${id}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch user'
      };
    }
  }

  // Get user by username
  async getUserByUsername(username) {
    try {
      const response = await apiClient.get(`/users/username/${username}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch user'
      };
    }
  }

  // Create new user
  async createUser(userData) {
    try {
      const response = await apiClient.post('/users', userData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create user'
      };
    }
  }

  // Update user
  async updateUser(id, userData) {
    try {
      const response = await apiClient.put(`/users/${id}`, userData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to update user'
      };
    }
  }

  // Delete user
  async deleteUser(id) {
    try {
      await apiClient.delete(`/users/${id}`);
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to delete user'
      };
    }
  }

  // Get users by role
  async getUsersByRole(role) {
    try {
      const response = await apiClient.get(`/users/role/${role}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch users by role'
      };
    }
  }

  // Get all staff members
  async getAllStaff() {
    try {
      const response = await apiClient.get('/users/staff');
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch staff members'
      };
    }
  }

  // Count users by role
  async countUsersByRole(role) {
    try {
      const response = await apiClient.get(`/users/count/role/${role}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to count users by role'
      };
    }
  }

  // Get staff by flight ID
  async getStaffByFlight(flightId) {
    try {
      const response = await apiClient.get(`/users/flight/${flightId}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch staff by flight'
      };
    }
  }

  // Check if user exists
  async userExists(username) {
    try {
      const response = await apiClient.get(`/users/exists/${username}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to check if user exists'
      };
    }
  }

  // Get admin users
  async getAdminUsers() {
    return this.getUsersByRole('admin');
  }

  // Get passenger users
  async getPassengerUsers() {
    return this.getUsersByRole('passenger');
  }

  // Get inflight staff users
  async getInflightStaff() {
    return this.getUsersByRole('inflightStaff');
  }

  // Get checkin staff users
  async getCheckinStaff() {
    return this.getUsersByRole('checkinStaff');
  }
}

// Create and export a singleton instance
const userService = new UserService();
export default userService;
