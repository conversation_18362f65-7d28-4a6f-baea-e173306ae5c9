{"configurations": [{"type": "java", "name": "TravelHistoryServiceApplication", "request": "launch", "mainClass": "com.oracle.travel_history_service.TravelHistoryServiceApplication", "projectName": ""}, {"type": "java", "name": "Spring Boot-UsermanagementApplication<usermanagement>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.oracle.usermanagement.UsermanagementApplication", "projectName": "usermanagement", "args": "", "envFile": "${workspaceFolder}/.env", "vmArgs": " -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=64645 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dspring.jmx.enabled=true -Djava.rmi.server.hostname=localhost -Dspring.application.admin.enabled=true -Dspring.boot.project.name=usermanagement"}, {"type": "java", "name": "Spring Boot-TravelHistoryServiceApplication<travel_history_service>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.oracle.travel_history_service.TravelHistoryServiceApplication", "projectName": "travel_history_service", "args": "", "envFile": "${workspaceFolder}/.env"}, {"type": "java", "name": "Spring Boot-ApiGatewayApplication<api-gateway>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.oracle.api_gateway.ApiGatewayApplication", "projectName": "api-gateway", "args": "", "envFile": "${workspaceFolder}/.env"}, {"type": "java", "name": "Spring Boot-Backend1Application<backend1>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.oracle.backend1.Backend1Application", "projectName": "backend1", "args": "", "envFile": "${workspaceFolder}/.env"}, {"type": "java", "name": "Spring Boot-EurekaServerApplication<eureka-server>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.oracle.eureka_server.EurekaServerApplication", "projectName": "eureka-server", "args": "", "envFile": "${workspaceFolder}/.env"}, {"type": "java", "name": "Spring Boot-FlightsApplication<flights>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.oracle.flights.FlightsApplication", "projectName": "flights", "args": "", "envFile": "${workspaceFolder}/.env"}, {"type": "java", "name": "Spring Boot-PassengersApplication<passengers>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.oracle.passengers.PassengersApplication", "projectName": "passengers", "args": "", "envFile": "${workspaceFolder}/.env"}, {"type": "java", "name": "Spring Boot-ServiceManagementApplication<service_management>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.oracle.service_management.ServiceManagementApplication", "projectName": "service_management", "args": "", "envFile": "${workspaceFolder}/.env"}]}