import { passengerService } from '../api'

// In-memory cache for passengers with listeners for reactive updates
let passengersCache = []
const listeners = new Set()

// Emit changes to all listeners
function emit() {
  listeners.forEach(fn => fn(passengersCache.slice()))
}

// Subscribe to passenger changes
export function subscribe(fn) {
  listeners.add(fn)
  return () => listeners.delete(fn)
}

// Get all passengers
export async function list() {
  try {
    const result = await passengerService.getAllPassengers()
    if (result.success) {
      passengersCache = result.data
      emit()
      return passengersCache.slice()
    } else {
      console.error('Failed to fetch passengers:', result.error)
      return passengersCache.slice() // Return cached data on error
    }
  } catch (error) {
    console.error('Error fetching passengers:', error)
    return passengersCache.slice()
  }
}

// Find passenger by name
export async function findByName(name) {
  // First try to find in cache
  let passenger = passengersCache.find(p => p.name === name)
  if (passenger) {
    return passenger
  }

  // If not in cache, refresh cache and try again
  try {
    await list()
    return passengersCache.find(p => p.name === name)
  } catch (error) {
    console.error('Error finding passenger by name:', error)
    return null
  }
}

// Find passenger by ID
export async function findById(id) {
  try {
    const result = await passengerService.getPassengerById(id)
    if (result.success) {
      return result.data
    } else {
      console.error('Failed to fetch passenger:', result.error)
      // Fallback to cache
      return passengersCache.find(p => p.id === Number(id))
    }
  } catch (error) {
    console.error('Error fetching passenger:', error)
    return passengersCache.find(p => p.id === Number(id))
  }
}

// Add or update passenger
export async function addOrUpdate(passengerData) {
  try {
    let result

    if (passengerData.id) {
      // Update existing passenger
      result = await passengerService.updatePassenger(passengerData.id, passengerData)
    } else {
      // Create new passenger
      result = await passengerService.createPassenger(passengerData)
    }

    if (result.success) {
      // Refresh the passengers list
      await list()
      return { success: true, data: result.data }
    } else {
      console.error('Failed to save passenger:', result.error)
      return { success: false, error: result.error }
    }
  } catch (error) {
    console.error('Error saving passenger:', error)
    return { success: false, error: error.message }
  }
}

// Remove passenger
export async function remove(id) {
  try {
    const result = await passengerService.deletePassenger(id)
    if (result.success) {
      // Refresh the passengers list
      await list()
      return { success: true }
    } else {
      console.error('Failed to delete passenger:', result.error)
      return { success: false, error: result.error }
    }
  } catch (error) {
    console.error('Error deleting passenger:', error)
    return { success: false, error: error.message }
  }
}

// Get passengers by flight ID
export async function getByFlightId(flightId, filters = {}) {
  try {
    const result = await passengerService.getPassengersByFlight(flightId, filters)
    if (result.success) {
      return result.data
    } else {
      console.error('Failed to fetch passengers by flight:', result.error)
      return []
    }
  } catch (error) {
    console.error('Error fetching passengers by flight:', error)
    return []
  }
}

// Initialize passengers cache on module load
list().catch(console.error)
