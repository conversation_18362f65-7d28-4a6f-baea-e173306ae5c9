# User Management Service

A Spring Boot microservice for managing user profiles and staff assignments in an airline management system.

## Features

- **User CRUD Operations**: Create, read, update, and delete users
- **Role-based User Management**: Support for admin, inflightStaff, checkinStaff, and passenger roles
- **Staff Assignment Tracking**: Track staff assignments to specific flights
- **Comprehensive API**: RESTful endpoints for all user management operations
- **Data Validation**: Input validation and error handling
- **Database Integration**: Oracle database integration with JPA/Hibernate

## Technology Stack

- **Java 17**
- **Spring Boot 3.5.4**
- **Spring Data JPA**
- **Spring Security**
- **Oracle Database**
- **Lombok**
- **Maven**

## Database Schema

The service uses the `users` table from the airline database schema:

```sql
CREATE TABLE users (
    user_id         NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    username        VARCHAR2(50) UNIQUE NOT NULL,
    password        VARCHAR2(255) NOT NULL,
    role            VARCHAR2(30) NOT NULL CHECK (role IN ('admin', 'inflightStaff', 'checkinStaff', 'passenger')),
    name            VARCHAR2(100) NOT NULL,
    email           VARCHAR2(100),
    phone_number    VARCHAR2(20),
    flight_id       NUMBER,  -- For staff members assigned to specific flights
    created_at      TIMESTAMP DEFAULT SYSTIMESTAMP,
    last_login      TIMESTAMP
);
```

## API Endpoints

### User CRUD Operations

#### Get All Users
```
GET /users
```
Returns a list of all users in the system.

#### Get User by ID
```
GET /users/{id}
```
Returns a specific user by their ID.

#### Get User by Username
```
GET /users/username/{username}
```
Returns a specific user by their username.

#### Create User
```
POST /users
Content-Type: application/json

{
    "username": "string",
    "password": "string",
    "role": "admin|inflightStaff|checkinStaff|passenger",
    "name": "string",
    "email": "string",
    "phoneNumber": "string",
    "flightId": number (optional, for staff)
}
```

#### Update User
```
PUT /users/{id}
Content-Type: application/json

{
    "username": "string",
    "password": "string",
    "role": "admin|inflightStaff|checkinStaff|passenger",
    "name": "string",
    "email": "string",
    "phoneNumber": "string",
    "flightId": number (optional, for staff)
}
```

#### Delete User
```
DELETE /users/{id}
```

### Role-based Queries

#### Get Users by Role
```
GET /users/role/{role}
```
Returns all users with the specified role (admin, inflightStaff, checkinStaff, passenger).

#### Get All Staff
```
GET /users/staff
```
Returns all staff members (inflightStaff and checkinStaff).

#### Count Users by Role
```
GET /users/count/role/{role}
```
Returns the count of users with the specified role.

### Flight Assignment Queries

#### Get Staff by Flight ID
```
GET /users/flight/{flightId}
```
Returns all staff members assigned to a specific flight.

### Utility Endpoints

#### Check if User Exists
```
GET /users/exists/{username}
```
Returns boolean indicating if a user with the given username exists.

## Response Format

All API responses follow a consistent format:

```json
{
    "success": boolean,
    "message": "string",
    "data": object|array|null,
    "timestamp": "ISO-8601 datetime"
}
```

### Success Response Example
```json
{
    "success": true,
    "message": "User retrieved successfully",
    "data": {
        "userId": 1,
        "username": "admin1",
        "role": "admin",
        "name": "Alice Admin",
        "email": "<EMAIL>",
        "phoneNumber": "************",
        "flightId": null,
        "createdAt": "2025-08-21T10:30:00",
        "lastLogin": "2025-08-21T09:15:00",
        "staff": false,
        "admin": true,
        "passenger": false
    },
    "timestamp": "2025-08-21T10:30:00"
}
```

### Error Response Example
```json
{
    "success": false,
    "message": "User not found with ID: 999",
    "data": null,
    "timestamp": "2025-08-21T10:30:00"
}
```

## Configuration

### Database Configuration
Update `application.properties` with your Oracle database connection details:

```properties
spring.datasource.url=***********************************
spring.datasource.username=your_username
spring.datasource.password=your_password
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
```

### Server Configuration
```properties
server.port=8081
```

## Running the Application

1. **Prerequisites**:
   - Java 17 or higher
   - Oracle Database with the airline schema
   - Maven 3.6 or higher

2. **Build the application**:
   ```bash
   mvn clean compile
   ```

3. **Run tests**:
   ```bash
   mvn test
   ```

4. **Start the application**:
   ```bash
   mvn spring-boot:run
   ```

The application will start on `http://localhost:8081`

## Testing

The service includes comprehensive unit and integration tests:

- **Controller Tests**: Test REST endpoints with MockMvc
- **Service Tests**: Test business logic with mocked dependencies
- **Repository Tests**: Test database operations

Run tests with:
```bash
mvn test
```

## Error Handling

The service includes comprehensive error handling:

- **UserNotFoundException**: When a user is not found (404)
- **UserAlreadyExistsException**: When trying to create a duplicate user (409)
- **IllegalArgumentException**: For invalid input data (400)
- **Validation Errors**: For request validation failures (400)
- **Generic Exception**: For unexpected errors (500)

## Security Considerations

- Passwords should be hashed before storage (implement password encoding)
- Consider implementing authentication and authorization
- Add rate limiting for API endpoints
- Implement audit logging for user operations

## Future Enhancements

- Password encryption/hashing
- JWT-based authentication
- Role-based access control
- Audit logging
- Pagination for large datasets
- Caching for frequently accessed data
- API documentation with Swagger/OpenAPI
