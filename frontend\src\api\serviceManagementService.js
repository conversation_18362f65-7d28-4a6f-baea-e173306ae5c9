// Service Management Service
// Handles all service management API operations (meals, baggage, shopping, etc.)

import apiClient from './apiClient.js';

class ServiceManagementService {
  // Get all services for a flight
  async getFlightServices(flightId) {
    try {
      const response = await apiClient.get(`/services/flight/${flightId}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flight services'
      };
    }
  }

  // Get flight passenger services with optional filtering
  async getFlightPassengerServices(flightId, serviceType = null) {
    try {
      const endpoint = serviceType 
        ? `/services/flight/${flightId}/passengers?serviceType=${serviceType}`
        : `/services/flight/${flightId}/passengers`;
      
      const response = await apiClient.get(endpoint);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flight passenger services'
      };
    }
  }

  // Get flight service statistics
  async getFlightServiceStats(flightId) {
    try {
      const response = await apiClient.get(`/services/flight/${flightId}/stats`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flight service statistics'
      };
    }
  }

  // Get passenger services
  async getPassengerServices(passengerId) {
    try {
      const response = await apiClient.get(`/services/passenger/${passengerId}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch passenger services'
      };
    }
  }

  // Create passenger service
  async createPassengerService(serviceData) {
    try {
      const response = await apiClient.post('/services/passenger', serviceData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create passenger service'
      };
    }
  }

  // Update passenger service
  async updatePassengerService(passengerId, serviceData) {
    try {
      const response = await apiClient.put(`/services/passenger/${passengerId}`, serviceData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to update passenger service'
      };
    }
  }

  // Delete passenger service
  async deletePassengerService(passengerId) {
    try {
      await apiClient.delete(`/services/passenger/${passengerId}`);
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to delete passenger service'
      };
    }
  }

  // MEAL SERVICES
  
  // Get meal services for a flight
  async getFlightMealServices(flightId, mealType = null) {
    try {
      const endpoint = mealType 
        ? `/services/meals/flight/${flightId}?mealType=${mealType}`
        : `/services/meals/flight/${flightId}`;
      
      const response = await apiClient.get(endpoint);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flight meal services'
      };
    }
  }

  // Create meal service
  async createMealService(mealData) {
    try {
      const response = await apiClient.post('/services/meals', mealData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create meal service'
      };
    }
  }

  // BAGGAGE SERVICES
  
  // Get baggage services for a flight
  async getFlightBaggageServices(flightId) {
    try {
      const response = await apiClient.get(`/services/baggage/flight/${flightId}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flight baggage services'
      };
    }
  }

  // Create baggage service
  async createBaggageService(baggageData) {
    try {
      const response = await apiClient.post('/services/baggage', baggageData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create baggage service'
      };
    }
  }

  // SHOPPING SERVICES
  
  // Get shopping services for a flight
  async getFlightShoppingServices(flightId) {
    try {
      const response = await apiClient.get(`/services/shopping/flight/${flightId}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flight shopping services'
      };
    }
  }

  // Create shopping service
  async createShoppingService(shoppingData) {
    try {
      const response = await apiClient.post('/services/shopping', shoppingData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create shopping service'
      };
    }
  }

  // CONVENIENCE METHODS

  // Get passengers with meal services
  async getPassengersWithMealServices(flightId) {
    return this.getFlightPassengerServices(flightId, 'meal');
  }

  // Get passengers with shopping services
  async getPassengersWithShoppingServices(flightId) {
    return this.getFlightPassengerServices(flightId, 'shopping');
  }

  // Get passengers with ancillary services
  async getPassengersWithAncillaryServices(flightId) {
    return this.getFlightPassengerServices(flightId, 'ancillary');
  }

  // Get passengers with baggage services
  async getPassengersWithBaggageServices(flightId) {
    return this.getFlightPassengerServices(flightId, 'baggage');
  }
}

// Create and export a singleton instance
const serviceManagementService = new ServiceManagementService();
export default serviceManagementService;
