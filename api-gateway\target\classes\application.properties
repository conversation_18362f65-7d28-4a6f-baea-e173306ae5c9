spring.application.name=api-gateway

# Server Configuration
server.port=8090

# Eureka Client Configuration
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.prefer-ip-address=true
eureka.instance.hostname=localhost

# Gateway Configuration
spring.cloud.gateway.discovery.locator.enabled=true
spring.cloud.gateway.discovery.locator.lower-case-service-id=true

# CORS Configuration
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-origins=http://localhost:5173,http://localhost:3000
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-headers=*
spring.cloud.gateway.globalcors.cors-configurations.[/**].allow-credentials=true

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,gateway
management.endpoint.health.show-details=always

# Logging Configuration
logging.level.org.springframework.cloud.gateway=DEBUG
logging.level.org.springframework.cloud.netflix.eureka=DEBUG
logging.level.com.netflix.discovery=DEBUG
logging.level.reactor.netty.http.client=DEBUG

# Gateway Timeout Configuration
spring.cloud.gateway.httpclient.connect-timeout=10000
spring.cloud.gateway.httpclient.response-timeout=30s

# Circuit Breaker Configuration (Optional)
resilience4j.circuitbreaker.instances.default.sliding-window-size=10
resilience4j.circuitbreaker.instances.default.minimum-number-of-calls=5
resilience4j.circuitbreaker.instances.default.failure-rate-threshold=50
