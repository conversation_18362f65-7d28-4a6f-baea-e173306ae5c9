// Authentication Service
// Handles user authentication and authorization

import apiClient from './apiClient.js';

class AuthService {
  // Login user with username and password
  async login(username, password) {
    try {
      const response = await apiClient.login(username, password);
      return {
        success: true,
        user: {
          username: response.username,
          role: response.role,
          name: response.name
        },
        token: response.token
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Login failed'
      };
    }
  }

  // Logout current user
  async logout() {
    try {
      await apiClient.logout();
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: error.message };
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    return apiClient.isAuthenticated();
  }

  // Get current user info
  getCurrentUser() {
    return apiClient.getUserInfo();
  }

  // Get current user role
  getCurrentUserRole() {
    return apiClient.getUserRole();
  }

  // Validate current token
  async validateToken() {
    try {
      return await apiClient.validateToken();
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  // Get user roles (requires authentication)
  async getUserRoles() {
    try {
      const response = await apiClient.get('/api/auth/roles');
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to get user roles'
      };
    }
  }

  // Check if user has specific role
  hasRole(role) {
    const currentRole = this.getCurrentUserRole();
    return currentRole === role;
  }

  // Check if user is admin
  isAdmin() {
    return this.hasRole('admin');
  }

  // Check if user is staff (inflight or checkin)
  isStaff() {
    const role = this.getCurrentUserRole();
    return role === 'inflightStaff' || role === 'checkinStaff';
  }

  // Check if user is passenger
  isPassenger() {
    return this.hasRole('passenger');
  }

  // Check if user is inflight staff
  isInflightStaff() {
    return this.hasRole('inflightStaff');
  }

  // Check if user is checkin staff
  isCheckinStaff() {
    return this.hasRole('checkinStaff');
  }
}

// Create and export a singleton instance
const authService = new AuthService();
export default authService;
