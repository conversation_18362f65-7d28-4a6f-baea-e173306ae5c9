// Flight Service
// Handles all flight-related API operations

import apiClient from './apiClient.js';

class FlightService {
  // Get all flights
  async getAllFlights() {
    try {
      const response = await apiClient.get('/flights');
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flights'
      };
    }
  }

  // Get flight by ID
  async getFlightById(id) {
    try {
      const response = await apiClient.get(`/flights/${id}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flight'
      };
    }
  }

  // Create new flight
  async createFlight(flightData) {
    try {
      const response = await apiClient.post('/flights', flightData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create flight'
      };
    }
  }

  // Update flight
  async updateFlight(id, flightData) {
    try {
      const response = await apiClient.put(`/flights/${id}`, flightData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to update flight'
      };
    }
  }

  // Delete flight
  async deleteFlight(id) {
    try {
      await apiClient.delete(`/flights/${id}`);
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to delete flight'
      };
    }
  }

  // Get flights by route
  async getFlightsByRoute(route) {
    try {
      const response = await apiClient.get(`/flights/route/${route}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flights by route'
      };
    }
  }

  // Get flights by date
  async getFlightsByDate(date) {
    try {
      const response = await apiClient.get(`/flights/date/${date}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flights by date'
      };
    }
  }

  // Get available flights
  async getAvailableFlights() {
    try {
      const response = await apiClient.get('/flights/available');
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch available flights'
      };
    }
  }

  // Get flight seat availability
  async getFlightSeats(flightId) {
    try {
      const response = await apiClient.get(`/flights/${flightId}/seats`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flight seats'
      };
    }
  }

  // Search flights
  async searchFlights(searchParams) {
    try {
      const queryParams = new URLSearchParams();
      
      if (searchParams.origin) queryParams.append('origin', searchParams.origin);
      if (searchParams.destination) queryParams.append('destination', searchParams.destination);
      if (searchParams.date) queryParams.append('date', searchParams.date);
      
      const queryString = queryParams.toString();
      const endpoint = queryString ? `/flights/search?${queryString}` : '/flights/search';
      
      const response = await apiClient.get(endpoint);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to search flights'
      };
    }
  }

  // Get flights with available seats by route
  async getAvailableFlightsByRoute(route) {
    try {
      const response = await apiClient.get(`/flights/route/${route}/available`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch available flights by route'
      };
    }
  }

  // Get flights with available seats by date
  async getAvailableFlightsByDate(date) {
    try {
      const response = await apiClient.get(`/flights/date/${date}/available`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch available flights by date'
      };
    }
  }
}

// Create and export a singleton instance
const flightService = new FlightService();
export default flightService;
