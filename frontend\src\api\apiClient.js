// API Client for Airline Management System
// Handles all HTTP requests through the API Gateway

const API_BASE_URL = 'http://localhost:8090'; // API Gateway URL

class ApiClient {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Get JWT token from localStorage
  getToken() {
    return localStorage.getItem('jwt_token');
  }

  // Set JWT token in localStorage
  setToken(token) {
    localStorage.setItem('jwt_token', token);
  }

  // Remove JWT token from localStorage
  removeToken() {
    localStorage.removeItem('jwt_token');
  }

  // Get user info from localStorage
  getUserInfo() {
    const userInfo = localStorage.getItem('user_info');
    return userInfo ? JSON.parse(userInfo) : null;
  }

  // Set user info in localStorage
  setUserInfo(userInfo) {
    localStorage.setItem('user_info', JSON.stringify(userInfo));
  }

  // Remove user info from localStorage
  removeUserInfo() {
    localStorage.removeItem('user_info');
  }

  // Create headers for API requests
  createHeaders(includeAuth = true) {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (includeAuth) {
      const token = this.getToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    return headers;
  }

  // Generic HTTP request method
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.createHeaders(options.includeAuth !== false),
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      // Handle different response types
      if (response.status === 204) {
        return null; // No content
      }

      const contentType = response.headers.get('content-type');
      let data;
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      if (!response.ok) {
        throw new Error(data.message || data || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // HTTP Methods
  async get(endpoint, options = {}) {
    return this.request(endpoint, { method: 'GET', ...options });
  }

  async post(endpoint, data, options = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options,
    });
  }

  async put(endpoint, data, options = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options,
    });
  }

  async delete(endpoint, options = {}) {
    return this.request(endpoint, { method: 'DELETE', ...options });
  }

  // Authentication methods
  async login(username, password) {
    try {
      const response = await this.post('/api/auth/login', { username, password }, { includeAuth: false });
      
      if (response.success && response.token) {
        this.setToken(response.token);
        this.setUserInfo({
          username: response.username,
          role: response.role,
          name: response.name
        });
        return response;
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async validateToken() {
    try {
      const response = await this.post('/api/auth/validate-token', {});
      return response.valid;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  async logout() {
    this.removeToken();
    this.removeUserInfo();
  }

  // Check if user is authenticated
  isAuthenticated() {
    const token = this.getToken();
    return !!token;
  }

  // Get current user role
  getUserRole() {
    const userInfo = this.getUserInfo();
    return userInfo ? userInfo.role : null;
  }
}

// Create and export a singleton instance
const apiClient = new ApiClient();
export default apiClient;
