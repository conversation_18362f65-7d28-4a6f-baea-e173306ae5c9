spring.application.name=usermanagement

# Server Configuration
server.port=8083

# Oracle Database Configuration
spring.datasource.url=************************************* 
spring.datasource.username=system
spring.datasource.password=Oracle2022
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver

# JPA/Hibernate Configuration
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Logging Configuration
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Jackson Configuration for JSON handling
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.time-zone=UTC

# JWT Configuration (must match backend1 for token validation)
app.jwt.secret=change-this-to-a-long-random-string
app.jwt.expiration-ms=3600000

# Eureka Client Configuration
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.prefer-ip-address=true
