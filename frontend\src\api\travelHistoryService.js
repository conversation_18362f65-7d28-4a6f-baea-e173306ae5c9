// Travel History Service
// Handles all travel history API operations

import apiClient from './apiClient.js';

class TravelHistoryService {
  // Get passenger travel history
  async getPassengerTravelHistory(passengerId) {
    try {
      const response = await apiClient.get(`/history/passenger/${passengerId}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch passenger travel history'
      };
    }
  }

  // Get booking by reference
  async getBookingByReference(reference) {
    try {
      const response = await apiClient.get(`/history/booking/${reference}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch booking by reference'
      };
    }
  }

  // Get travel history for a specific flight
  async getFlightTravelHistory(flightId) {
    try {
      const response = await apiClient.get(`/history/flight/${flightId}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch flight travel history'
      };
    }
  }

  // Get recent travel history for a passenger
  async getRecentTravelHistory(passengerId) {
    try {
      const response = await apiClient.get(`/history/passenger/${passengerId}/recent`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch recent travel history'
      };
    }
  }

  // Get travel history by status
  async getTravelHistoryByStatus(passengerId, status) {
    try {
      const response = await apiClient.get(`/history/passenger/${passengerId}/status/${status}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch travel history by status'
      };
    }
  }

  // Get completed travel history
  async getCompletedTravelHistory(passengerId) {
    return this.getTravelHistoryByStatus(passengerId, 'Completed');
  }

  // Get pending travel history
  async getPendingTravelHistory(passengerId) {
    return this.getTravelHistoryByStatus(passengerId, 'Pending');
  }

  // Get cancelled travel history
  async getCancelledTravelHistory(passengerId) {
    return this.getTravelHistoryByStatus(passengerId, 'Cancelled');
  }

  // Get checked-in travel history
  async getCheckedInTravelHistory(passengerId) {
    return this.getTravelHistoryByStatus(passengerId, 'Checked-in');
  }

  // Get booked travel history
  async getBookedTravelHistory(passengerId) {
    return this.getTravelHistoryByStatus(passengerId, 'Booked');
  }

  // Create travel history record
  async createTravelHistory(travelData) {
    try {
      const response = await apiClient.post('/history', travelData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create travel history record'
      };
    }
  }

  // Update travel history record
  async updateTravelHistory(historyId, travelData) {
    try {
      const response = await apiClient.put(`/history/${historyId}`, travelData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to update travel history record'
      };
    }
  }

  // Delete travel history record
  async deleteTravelHistory(historyId) {
    try {
      await apiClient.delete(`/history/${historyId}`);
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to delete travel history record'
      };
    }
  }

  // Get travel statistics for a passenger
  async getPassengerTravelStats(passengerId) {
    try {
      const historyResult = await this.getPassengerTravelHistory(passengerId);
      
      if (!historyResult.success) {
        return historyResult;
      }

      const history = historyResult.data;
      const stats = {
        totalTrips: history.length,
        completedTrips: history.filter(h => h.status === 'Completed').length,
        cancelledTrips: history.filter(h => h.status === 'Cancelled').length,
        pendingTrips: history.filter(h => h.status === 'Pending').length,
        totalDistance: history.reduce((sum, h) => sum + (h.distanceKm || 0), 0),
        totalDuration: history.reduce((sum, h) => sum + (h.durationMin || 0), 0),
        favoriteDestinations: this.getFavoriteDestinations(history),
        recentTrips: history.slice(0, 5) // Last 5 trips
      };

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to calculate travel statistics'
      };
    }
  }

  // Helper method to get favorite destinations
  getFavoriteDestinations(history) {
    const destinations = {};
    history.forEach(h => {
      if (h.destination) {
        destinations[h.destination] = (destinations[h.destination] || 0) + 1;
      }
    });

    return Object.entries(destinations)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([destination, count]) => ({ destination, count }));
  }
}

// Create and export a singleton instance
const travelHistoryService = new TravelHistoryService();
export default travelHistoryService;
