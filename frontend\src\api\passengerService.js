// Passenger Service
// Handles all passenger-related API operations

import apiClient from './apiClient.js';

class PassengerService {
  // Get all passengers
  async getAllPassengers() {
    try {
      const response = await apiClient.get('/passengers');
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch passengers'
      };
    }
  }

  // Get passenger by ID
  async getPassengerById(id) {
    try {
      const response = await apiClient.get(`/passengers/${id}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch passenger'
      };
    }
  }

  // Create new passenger
  async createPassenger(passengerData) {
    try {
      const response = await apiClient.post('/passengers', passengerData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create passenger'
      };
    }
  }

  // Update passenger
  async updatePassenger(id, passengerData) {
    try {
      const response = await apiClient.put(`/passengers/${id}`, passengerData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to update passenger'
      };
    }
  }

  // Delete passenger
  async deletePassenger(id) {
    try {
      await apiClient.delete(`/passengers/${id}`);
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to delete passenger'
      };
    }
  }

  // Get passengers by flight ID
  async getPassengersByFlight(flightId, filters = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters.checkedIn !== undefined) queryParams.append('checkedIn', filters.checkedIn);
      if (filters.specialNeeds) queryParams.append('specialNeeds', 'true');
      if (filters.missingInfo) queryParams.append('missingInfo', 'true');
      
      const queryString = queryParams.toString();
      const endpoint = queryString ? `/passengers/flight/${flightId}?${queryString}` : `/passengers/flight/${flightId}`;
      
      const response = await apiClient.get(endpoint);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch passengers by flight'
      };
    }
  }

  // Get checked-in passengers for a flight
  async getCheckedInPassengers(flightId) {
    try {
      const response = await apiClient.get(`/passengers/flight/${flightId}/checkedin`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch checked-in passengers'
      };
    }
  }

  // Get not checked-in passengers for a flight
  async getNotCheckedInPassengers(flightId) {
    try {
      const response = await apiClient.get(`/passengers/flight/${flightId}/not-checkedin`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch not checked-in passengers'
      };
    }
  }

  // Get passengers with special needs for a flight
  async getSpecialNeedsPassengers(flightId) {
    try {
      const response = await apiClient.get(`/passengers/flight/${flightId}/special-needs`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch special needs passengers'
      };
    }
  }

  // Check in passenger
  async checkInPassenger(passengerId, checkInData) {
    try {
      const response = await apiClient.post(`/passengers/${passengerId}/checkin`, checkInData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to check in passenger'
      };
    }
  }

  // Get passenger bookings
  async getPassengerBookings(passengerId) {
    try {
      const response = await apiClient.get(`/passengers/${passengerId}/bookings`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch passenger bookings'
      };
    }
  }

  // Create passenger booking
  async createPassengerBooking(passengerId, bookingData) {
    try {
      const response = await apiClient.post(`/passengers/${passengerId}/bookings`, bookingData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create passenger booking'
      };
    }
  }

  // Get booking by ID
  async getBookingById(bookingId) {
    try {
      const response = await apiClient.get(`/bookings/${bookingId}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch booking'
      };
    }
  }
}

// Create and export a singleton instance
const passengerService = new PassengerService();
export default passengerService;
